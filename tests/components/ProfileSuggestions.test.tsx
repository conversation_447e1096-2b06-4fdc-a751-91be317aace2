import React from 'react';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { NextIntlClientProvider } from 'next-intl';
import { ProfileSuggestions } from '@/components/social/ProfileSuggestions';
import { ProfileSuggestionsCarousel } from '@/components/social/ProfileSuggestionsCarousel';

// Mock de fetch
global.fetch = vi.fn();

const mockMessages = {
  Social: {
    suggestions: {
      title: 'Suggestions pour vous',
      mutualFollowers: '{count, plural, =1 {1 ami en commun} other {{count} amis en commun}}',
    },
  },
};

const mockSuggestionsResponse = {
  success: true,
  suggestions: [
    {
      id: 'default-user',
      name: '<PERSON>',
      image: 'https://example.com/avatar.jpg',
      publicListId: 'public-123',
      mutualFollowers: 0,
      isDefaultProfile: true,
    },
    {
      id: 'user-1',
      name: '<PERSON>',
      image: 'https://example.com/john.jpg',
      publicListId: 'public-456',
      mutualFollowers: 3,
      isDefaultProfile: false,
    },
    {
      id: 'user-2',
      name: '<PERSON> <PERSON>',
      image: null,
      publicListId: 'public-789',
      mutualFollowers: 1,
      isDefaultProfile: false,
    },
  ],
};

function renderWithIntl(component: React.ReactElement) {
  return render(
    <NextIntlClientProvider locale="fr" messages={mockMessages}>
      {component}
    </NextIntlClientProvider>
  );
}

describe('ProfileSuggestions (Desktop)', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (global.fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockSuggestionsResponse),
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('devrait afficher le titre des suggestions', async () => {
    renderWithIntl(<ProfileSuggestions />);

    await waitFor(() => {
      expect(screen.getByText('Suggestions pour vous')).toBeInTheDocument();
    });
  });

  it('devrait afficher le profil par défaut sans icône couronne', async () => {
    renderWithIntl(<ProfileSuggestions />);

    await waitFor(() => {
      expect(screen.getByText('Simon Gavelle')).toBeInTheDocument();
      // Plus de texte "Fondateur" affiché
      expect(screen.queryByText('Fondateur de Stream2Spin')).not.toBeInTheDocument();
    });
  });

  it('devrait afficher les connexions mutuelles pour les autres utilisateurs', async () => {
    renderWithIntl(<ProfileSuggestions />);

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('3 amis en commun')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.getByText('1 ami en commun')).toBeInTheDocument();
    });
  });

  it('devrait afficher les boutons "Suivre" pour chaque suggestion', async () => {
    renderWithIntl(<ProfileSuggestions />);

    await waitFor(() => {
      const followButtons = screen.getAllByRole('button');
      // Au moins 3 boutons (un pour chaque suggestion)
      expect(followButtons.length).toBeGreaterThanOrEqual(3);
    });
  });

  it('devrait gérer les erreurs de l\'API gracieusement', async () => {
    (global.fetch as any).mockRejectedValue(new Error('API Error'));

    renderWithIntl(<ProfileSuggestions />);

    // Le composant ne devrait pas planter et afficher l'état de chargement puis rien
    await waitFor(() => {
      expect(screen.queryByText('Suggestions pour vous')).toBeInTheDocument();
    });
  });
});

describe('ProfileSuggestionsCarousel (Mobile)', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (global.fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockSuggestionsResponse),
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('devrait afficher le carrousel avec le titre', async () => {
    renderWithIntl(<ProfileSuggestionsCarousel />);

    await waitFor(() => {
      expect(screen.getByText('Suggestions pour vous')).toBeInTheDocument();
    });
  });

  it('devrait afficher les suggestions en format carrousel', async () => {
    renderWithIntl(<ProfileSuggestionsCarousel />);

    await waitFor(() => {
      expect(screen.getByText('Simon Gavelle')).toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });
  });

  it('devrait afficher les avatars sans icônes couronne', async () => {
    renderWithIntl(<ProfileSuggestionsCarousel />);

    await waitFor(() => {
      // Vérifier que Simon Gavelle est affiché sans le texte "Fondateur"
      expect(screen.getByText('Simon Gavelle')).toBeInTheDocument();
      expect(screen.queryByText('Fondateur de Stream2Spin')).not.toBeInTheDocument();
    });
  });

  it('ne devrait rien afficher si aucune suggestion', async () => {
    (global.fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ success: true, suggestions: [] }),
    });

    const { container } = renderWithIntl(<ProfileSuggestionsCarousel />);

    await waitFor(() => {
      // Le composant ne devrait rien rendre
      expect(container.firstChild).toBeNull();
    });
  });

  it('devrait afficher le layout compact avec photo et bouton', async () => {
    renderWithIntl(<ProfileSuggestionsCarousel />);

    await waitFor(() => {
      // Vérifier que les noms sont affichés
      expect(screen.getByText('Simon Gavelle')).toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();

      // Vérifier que les boutons "Suivre" sont présents
      const followButtons = screen.getAllByRole('button');
      expect(followButtons.length).toBeGreaterThanOrEqual(3);
    });
  });
});
