import { vi } from 'vitest';
import '@testing-library/jest-dom';
import React from 'react';

// Rendre React disponible globalement pour les tests
(global as any).React = React;

// Mock de next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    prefetch: vi.fn(),
  }),
  useSearchParams: () => ({
    get: vi.fn(),
    getAll: vi.fn(),
    has: vi.fn(),
    keys: vi.fn(),
    values: vi.fn(),
    entries: vi.fn(),
    forEach: vi.fn(),
    toString: vi.fn(),
  }),
  usePathname: () => '/test-path',
}));

// Mock de next/link
vi.mock('next/link', () => {
  return {
    __esModule: true,
    default: ({ children, href, ...props }: any) => {
      return React.createElement('a', { href, ...props }, children);
    },
  };
});



// Mock des variables d'environnement
vi.stubEnv('NODE_ENV', 'test');
vi.stubEnv('NEXTAUTH_SECRET', 'test-secret');
vi.stubEnv('NEXTAUTH_URL', 'http://localhost:3000');
vi.stubEnv('DATABASE_URL', 'postgresql://test:test@localhost:5432/test');
vi.stubEnv('RESEND_API_KEY', 'test-resend-key');
