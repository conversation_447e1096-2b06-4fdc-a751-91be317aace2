"use client";

import Image from 'next/image';
import { AlbumArtGrid } from './album-art-grid';
import { cn } from '@/lib/utils';
import { useState, useEffect } from 'react';

interface SplashScreenProps {
  isMounted: boolean;
}

export function SplashScreen({ isMounted }: SplashScreenProps) {
  const [showDecorations, setShowDecorations] = useState(false);

  useEffect(() => {
    // <PERSON>é<PERSON> pour afficher les décorations après le logo principal
    // Cela permet une transition plus fluide depuis le splash natif
    const timer = setTimeout(() => {
      setShowDecorations(true);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div
      className={cn(
        'fixed inset-0 z-[100] flex items-center justify-center transition-opacity duration-500',
        // Utiliser exactement la même couleur que le manifest.json (#111827 = bg-gray-900)
        'bg-gray-900',
        isMounted ? 'opacity-100' : 'opacity-0 pointer-events-none'
      )}
    >
      {/* Décorations qui apparaissent progressivement */}
      <AlbumArtGrid
        className={cn(
          'transition-opacity duration-700',
          showDecorations ? 'opacity-20' : 'opacity-0'
        )}
      />
      <div
        className={cn(
          'absolute inset-0 backdrop-blur-md transition-opacity duration-700',
          showDecorations ? 'bg-black/50' : 'bg-black/0'
        )}
      />

      {/* Logo principal - apparaît immédiatement pour une transition fluide */}
      <div className="relative z-10 animate-pulse-slow">
        <Image
          src="/stream2spin-logo.svg"
          alt="Stream2Spin Logo"
          width={256}
          height={256}
          priority
        />
      </div>
    </div>
  );
}
