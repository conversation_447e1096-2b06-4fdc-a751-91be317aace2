'use client';

import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { User, UserPlus } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { FollowButton } from './FollowButton';

// Interface pour les suggestions depuis la nouvelle API
interface ProfileSuggestion {
  id: string;
  name: string | null;
  image: string | null;
  publicListId: string | null;
  mutualFollowers: number;
  isDefaultProfile?: boolean;
}

/**
 * Composant carrousel horizontal pour les suggestions de profils
 * Optimisé pour mobile/tablet avec style "stories"
 */
export function ProfileSuggestionsCarousel() {
  const t = useTranslations('Social');
  const [suggestions, setSuggestions] = useState<ProfileSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSuggestions = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/social/suggestions');
        const data = await response.json();
        
        if (data.success) {
          setSuggestions(data.suggestions);
        } else {
          console.error("Erreur API suggestions:", data.error);
        }
      } catch (error) {
        console.error("Erreur lors de la récupération des suggestions:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSuggestions();
  }, []);

  // Fonction pour retirer une suggestion après un follow réussi
  const handleFollowSuccess = (userId: string) => {
    setSuggestions(prev => prev.filter(suggestion => suggestion.id !== userId));
  };

  if (isLoading) {
    return (
      <div className="mb-4">
        <h3 className="font-semibold text-sm mb-3 px-4 flex items-center gap-2">
          <UserPlus className="h-4 w-4" />
          {t('suggestions.title')}
        </h3>
        <div className="flex gap-3 overflow-x-auto px-4 pb-2 scrollbar-hide">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex-shrink-0 flex items-center gap-2 bg-background/50 rounded-lg p-2">
              <div className="h-8 w-8 bg-muted rounded-full animate-pulse"></div>
              <div className="flex flex-col gap-1">
                <div className="h-3 w-16 bg-muted rounded-md animate-pulse"></div>
                <div className="h-5 w-12 bg-muted rounded-md animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (suggestions.length === 0) {
    return null; // Ne rien afficher si pas de suggestions
  }

  return (
    <div className="mb-4">
      <h3 className="font-semibold text-sm mb-3 px-4 flex items-center gap-2">
        <UserPlus className="h-4 w-4" />
        {t('suggestions.title')}
      </h3>
      <div className="flex gap-3 overflow-x-auto px-4 pb-2 scrollbar-hide">
        {suggestions.map((user) => (
          <div key={user.id} className="flex-shrink-0 flex items-center gap-2 bg-background/50 backdrop-blur-sm rounded-lg p-2 border">
            <Link href={`/u/${user.publicListId}`}>
              <Avatar className="h-8 w-8 border-2 border-transparent hover:border-primary/50 transition-colors">
                <AvatarImage src={user.image || ''} alt={user.name || 'Avatar'} />
                <AvatarFallback>
                  <User className="h-4 w-4" />
                </AvatarFallback>
              </Avatar>
            </Link>

            <div className="flex flex-col gap-1 min-w-0">
              <Link
                href={`/u/${user.publicListId}`}
                className="text-xs font-medium truncate hover:underline max-w-[80px]"
                title={user.name || 'Utilisateur'}
              >
                {user.name}
              </Link>

              <FollowButton
                targetUserId={user.id}
                isFollowingInitial={false}
                size="sm"
                className="text-xs px-2 py-1 h-5 text-[10px]"
                onFollowAction={() => handleFollowSuccess(user.id)}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
