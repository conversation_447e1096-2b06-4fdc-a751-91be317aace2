"use client";

import { useState, useEffect, createContext, useContext } from 'react';
import { SplashScreen } from '@/components/layout/splash-screen';
import { usePathname } from 'next/navigation';

const SplashScreenContext = createContext<null | boolean>(null);

/**
 * Hook pour détecter si l'application est lancée en mode PWA standalone
 */
function useIsPWAStandalone() {
  const [isPWA, setIsPWA] = useState(false);
  const [isChecked, setIsChecked] = useState(false);

  useEffect(() => {
    // Vérifier si l'app est en mode standalone (PWA)
    const checkPWAMode = () => {
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      const isIOSStandalone = (window.navigator as any).standalone === true;

      setIsPWA(isStandalone || isIOSStandalone);
      setIsChecked(true);
    };

    checkPWAMode();
  }, []);

  return { isPWA, isChecked };
}

export function SplashScreenProvider({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const [isMounted, setIsMounted] = useState(true);
  const [isFinished, setIsFinished] = useState(false);
  const { isPWA, isChecked } = useIsPWAStandalone();

  useEffect(() => {
    // Ne démarrer les timers que si on a vérifié le mode PWA
    if (!isChecked) return;

    // Si ce n'est pas une PWA, terminer immédiatement
    if (!isPWA) {
      setIsFinished(true);
      return;
    }

    // Sinon, démarrer la séquence de splash screen normale
    // Timing optimisé pour une transition fluide depuis le splash natif
    const timer = setTimeout(() => {
      setIsMounted(false);
    }, 2000); // Durée légèrement réduite pour éviter la lassitude

    const finishTimer = setTimeout(() => {
      setIsFinished(true);
    }, 2500); // Transition plus rapide

    return () => {
      clearTimeout(timer);
      clearTimeout(finishTimer);
    };
  }, [isPWA, isChecked, pathname]);

  // Attendre la vérification du mode PWA
  if (!isChecked) {
    return <>{children}</>;
  }

  // Si ce n'est pas une PWA ou si le splash est terminé, afficher directement les enfants
  if (!isPWA || isFinished) {
    return <>{children}</>;
  }

  // Pendant le chargement, on affiche le splash screen.
  return (
    <SplashScreenContext.Provider value={true}>
      <SplashScreen isMounted={isMounted} />
      {/* On peut choisir de ne pas rendre les enfants pendant le splash pour optimiser */}
    </SplashScreenContext.Provider>
  );
}

export const useSplashScreen = () => {
  return useContext(SplashScreenContext);
};
