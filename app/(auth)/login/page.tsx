import { SpotifySignInButton } from "@/components/auth/spotify-signin-button";
import { LanguageSwitcher } from "@/components/auth/language-switcher";
import { getSession } from "@/lib/auth";
import { redirect } from "next/navigation";

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';
import Link from "next/link";
import Image from "next/image";
import { getTranslations } from 'next-intl/server';

export default async function LoginPage() {
  // Vérifier si l'utilisateur est déjà connecté
  const session = await getSession();
  if (session) {
    // Rediriger vers la page de redirection intelligente
    redirect("/login-redirect");
  }

  const t = await getTranslations('login');

  return (
    <div
      className="min-h-screen flex items-center justify-center p-4 relative"
      style={{
        backgroundImage: "url('/erik-mclean-9y1cTVKe1IY-unsplash.jpg')",
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat"
      }}
    >
      {/* Overlay sombre pour améliorer le contraste */}
      <div className="absolute inset-0 bg-black/30"></div>

      {/* Switch de langue en haut à droite */}
      <div className="absolute top-4 right-4 z-20">
        <LanguageSwitcher />
      </div>

      {/* Carte glassmorphism centrée */}
      <div className="relative z-10 w-full max-w-md">
        <div
          className="backdrop-blur-xl bg-white/95 border border-white/30 rounded-3xl p-8 shadow-2xl"
          style={{
            backdropFilter: "blur(20px)",
            WebkitBackdropFilter: "blur(20px)",
          }}
        >
          <div className="space-y-8 text-center">
            {/* Logo */}
            <div className="flex justify-center">
              <Image
                src="/stream2spin-logo.svg"
                alt="Stream2Spin"
                width={240}
                height={90}
                className="h-16 w-auto object-contain"
                priority
              />
            </div>

            {/* Phrase d'accroche */}
            <div>
              <p
                className="text-lg font-medium text-gray-800 leading-relaxed"
                dangerouslySetInnerHTML={{ __html: t('tagline') }}
              />
            </div>

            {/* Bouton de connexion Spotify */}
            <div className="space-y-4">
              <SpotifySignInButton />
            </div>

            {/* Texte légal */}
            <div className="text-center">
              <p className="text-xs text-gray-600 leading-relaxed">
                {t('legalTextPrefix')}{" "}
                <Link
                  href="/terms"
                  target="_blank"
                  className="text-gray-700 underline hover:text-gray-900 transition-colors"
                >
                  {t('termsLink')}
                </Link>{" "}
                {t('legalTextMiddle')}{" "}
                <Link
                  href="/privacy"
                  target="_blank"
                  className="text-gray-700 underline hover:text-gray-900 transition-colors"
                >
                  {t('privacyLink')}
                </Link>
                {t('legalTextSuffix')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
