import { NextRequest, NextResponse } from 'next/server';
import { getFollowing } from '@/app/actions/social';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const targetUserId = searchParams.get('userId');

    if (!targetUserId) {
      return NextResponse.json(
        { success: false, error: 'ID utilisateur manquant' },
        { status: 400 }
      );
    }

    const following = await getFollowing(targetUserId);

    // ✅ CORRECTION: Wrapper le résultat dans un objet avec success
    return NextResponse.json({
      success: true,
      following: following
    });

  } catch (error) {
    console.error('Erreur API following:', error);
    return NextResponse.json(
      { success: false, error: 'Erreur serveur' },
      { status: 500 }
    );
  }
} 