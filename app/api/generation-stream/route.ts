import { NextRequest } from "next/server";
import { getValidSpotifyToken, fetchUserTopTracks, analyzeTracksAndCalculateScores } from "@/lib/spotify";

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';
import { db } from "@/lib/db";
import { recommendations, userDiscogsCollection, wishlistItems } from "@/lib/db/schema";
import { eq, and, desc, sql } from "drizzle-orm";
import { enrichWithAmazonSearchLinks } from "@/lib/amazon-phase1";
import { triggerFirstRecommendationEmailIfNeeded } from "@/app/actions/user";
import { getTranslations } from "next-intl/server";
// import { searchVinylOffers } from "@/lib/rakuten"; // Temporairement désactivé

/**
 * API Route SSE pour la génération interactive de recommandations
 * Epic 11 - US 11.1 & 11.2: Processus de génération streamé en temps réel
 */
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const userId = searchParams.get("userId");
  const lang = searchParams.get("lang") || "en";

  if (!userId) {
    return new Response("userId requis", { status: 400 });
  }

  // Initialiser les traductions pour la langue de l'utilisateur
  let t: any;
  try {
    t = await getTranslations({ locale: lang, namespace: 'generating' });
  } catch (error) {
    // Fallback si getTranslations échoue (par exemple pendant la génération statique)
    console.warn('Failed to load translations, using fallback');
    t = (key: string, params?: any) => {
      // Traductions de fallback en anglais
      const fallbackMessages: Record<string, string> = {
        'messages.analyzingListening': 'Analyzing your listening habits...',
        'messages.cleanupCompleted': 'Cleanup completed',
        'messages.collectionSynced': 'Collection synced',
        'messages.noRecommendationsGenerated': 'No recommendations could be generated',
        'messages.likeArtist': `You seem to like ${params?.artist || 'this artist'}`,
        'messages.likeAlbum': `Discovering ${params?.album || 'album'} by ${params?.artist || 'artist'}`,
        'messages.analysisCompleted': `Analysis completed with ${params?.count || 0} albums`,
        'messages.searchingOffers': 'Searching for offers...',
        'messages.amazonLinksGenerated': 'Amazon links generated',
        'messages.finalizingRecommendations': 'Finalizing recommendations...',
        'messages.recommendationsReady': `${params?.count || 0} recommendations ready`,
        'messages.foundPerfectAlbums': `Found ${params?.count || 0} perfect albums for you`,
        'messages.yourRecommendationsAreReady': 'Your recommendations are ready!',
        'messages.genericError': 'An error occurred during generation'
      };
      return fallbackMessages[key] || key;
    };
  }

  // Configuration SSE
  const headers = {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control',
  };

  const encoder = new TextEncoder();
  
  const stream = new ReadableStream({
    start(controller) {
      // Fonction utilitaire pour envoyer des événements
      let isControllerClosed = false;

      // Détecter la déconnexion du client
      const checkConnection = () => {
        if (isControllerClosed) return false;
        try {
          // Test simple pour vérifier si le controller est encore actif
          // Utiliser un message vide qui ne sera pas traité côté client
          controller.enqueue(encoder.encode(': heartbeat\n\n'));
          return true;
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
          console.log("🔒 Connexion client fermée détectée");
          }
          isControllerClosed = true;
          return false;
        }
      };

      const sendEvent = (data: any) => {
        if (!isControllerClosed) {
          try {
            const message = `data: ${JSON.stringify(data)}\n\n`;
            controller.enqueue(encoder.encode(message));
          } catch (error) {
            console.error("❌ Erreur lors de l'envoi d'événement SSE:", error);
            // Marquer le controller comme fermé pour éviter d'autres tentatives
            if (error instanceof Error && (
              error.message?.includes('Controller is already closed') ||
              error.message?.includes('network error') ||
              error.message?.includes('aborted')
            )) {
              isControllerClosed = true;
              if (process.env.NODE_ENV === 'development') {
              console.log("🔒 Controller SSE marqué comme fermé - client déconnecté");
              }
            }
          }
        }
      };

      // Vérification périodique de la connexion
      const connectionCheckInterval = setInterval(() => {
        if (!checkConnection()) {
          if (process.env.NODE_ENV === 'development') {
          console.log("🔒 Connexion fermée détectée - arrêt du processus");
          }
          clearInterval(connectionCheckInterval);
          return;
        }
      }, 5000); // Vérifier toutes les 5 secondes

      // Fonction principale de génération
      const generateRecommendations = async () => {
        try {
          if (process.env.NODE_ENV === 'development') {
          console.log(`🎵 Démarrage de la génération interactive pour l'utilisateur ${userId}`);
          }

          // Vérifier la connexion avant de commencer
          if (!checkConnection()) {
            if (process.env.NODE_ENV === 'development') {
            console.log("🔒 Connexion fermée avant le début - arrêt");
            }
            clearInterval(connectionCheckInterval);
            return;
          }

          // Vérification du token Spotify (en arrière-plan)
          const validAccessToken = await getValidSpotifyToken(userId);
          if (!validAccessToken) {
            throw new Error("Impossible d'obtenir un token Spotify valide");
          }

          // ÉTAPE 1: Analyse des écoutes
          sendEvent({
            type: 'step_update',
            step: 'analyzing',
            status: 'in_progress',
            message: t('messages.analyzingListening')
          });

          // Pas de status_update ici - on laisse place aux messages de découverte

          const timeframes = ['short_term', 'medium_term', 'long_term'] as const;
          let allRecommendations: any[] = [];
          let totalTracksProcessed = 0;
          let totalAlbumsAnalyzed = 0;
          let discoveredAlbums: any[] = [];

          // Avec la nouvelle structure de wishlist basée sur l'album,
          // la wishlist n'est plus supprimée automatiquement quand les recommandations sont supprimées
          // car il n'y a plus de clé étrangère vers la table recommendations

          // Supprimer les anciennes recommandations
          await db.delete(recommendations).where(eq(recommendations.userId, userId));

          sendEvent({
            type: 'insight',
            message: t('messages.cleanupCompleted')
          });

          // Récupérer la collection Discogs une seule fois en parallèle
          const userCollectionPromise = db.query.userDiscogsCollection.findMany({
            where: eq(userDiscogsCollection.userId, userId)
          });

          // OPTIMISATION: Traiter tous les timeframes en parallèle
          const timeframePromises = timeframes.map(async (timeframe) => {
            try {
              const topTracks = await fetchUserTopTracks(validAccessToken, timeframe);
              if (!topTracks || topTracks.length === 0) return { timeframe, tracks: [], albums: [], recommendations: [] };

              // Extraire les albums uniques pour l'affichage
              const uniqueAlbums = Array.from(new Set(topTracks.map(t => t.album.id)))
                .slice(0, 50)
                .map(albumId => {
                  const track = topTracks.find(t => t.album.id === albumId);
                  return {
                    artist: track?.artists[0]?.name || '',
                    album: track?.album.name || '',
                    imageUrl: track?.album.images[0]?.url || ''
                  };
                })
                .filter(album => album.imageUrl);

              // Analyser les recommandations
              const albumRecommendations = analyzeTracksAndCalculateScores(topTracks, timeframe);

              if (process.env.NODE_ENV === 'development') {
              console.log(`📀 Timeframe ${timeframe}: ${topTracks.length} tracks, ${uniqueAlbums.length} albums, ${albumRecommendations.length} recommandations`);
              }

              return {
                timeframe,
                tracks: topTracks,
                albums: uniqueAlbums,
                recommendations: albumRecommendations
              };
            } catch (error) {
              console.error(`❌ Erreur timeframe ${timeframe}:`, error);
              return { timeframe, tracks: [], albums: [], recommendations: [] };
            }
          });

          // Attendre tous les timeframes en parallèle
          const timeframeResults = await Promise.all(timeframePromises);

          // Récupérer la collection Discogs
          const userCollection = await userCollectionPromise;

          // Traiter les résultats et envoyer les albums découverts
          for (const result of timeframeResults) {
            if (result.tracks.length === 0) continue;

            // Mettre à jour les compteurs
            totalTracksProcessed += result.tracks.length;
            totalAlbumsAnalyzed += result.recommendations.length;

            sendEvent({
              type: 'counter_update',
              entity: 'tracks_processed',
              value: totalTracksProcessed
            });

            sendEvent({
              type: 'counter_update',
              entity: 'albums_analyzed',
              value: totalAlbumsAnalyzed
            });

            // Insight personnalisé pour le premier timeframe avec des données
            if (result.tracks.length > 0 && discoveredAlbums.length === 0) {
              const topArtist = result.tracks[0].artists[0]?.name;
              if (topArtist) {
                              sendEvent({
                type: 'insight',
                message: t('messages.likeArtist', { artist: topArtist })
              });
              }
            }

            // OPTIMISATION: Envoyer tous les albums immédiatement sans délai
            for (const album of result.albums) {
              discoveredAlbums.push(album);

              sendEvent({
                type: 'album_art_discovered',
                payload: album
              });

              // Message dynamique pour chaque découverte (envoi immédiat)
              sendEvent({
                type: 'status_update',
                message: t('messages.likeAlbum', { album: album.album, artist: album.artist })
              });
            }

            // Croiser avec la collection Discogs
            const recommendationsWithOwnership = result.recommendations.map(rec => ({
              ...rec,
              userId,
              isOwned: userCollection.some(item =>
                item.artistName.toLowerCase().includes(rec.artistName.toLowerCase()) &&
                item.albumTitle.toLowerCase().includes(rec.albumTitle.toLowerCase())
              ),
              generatedAt: new Date(),
            }));

            allRecommendations.push(...recommendationsWithOwnership);
          }

          if (process.env.NODE_ENV === 'development') {
          console.log(`📀 Total albums découverts: ${discoveredAlbums.length}`);
          }
          console.log(`📊 Total recommandations: ${allRecommendations.length}`);

          sendEvent({
            type: 'step_update',
            step: 'analyzing',
            status: 'completed',
            message: t('messages.analysisCompleted', { count: totalAlbumsAnalyzed })
          });

          sendEvent({
            type: 'step_update',
            step: 'collection',
            status: 'completed',
            message: t('messages.collectionSynced')
          });

          if (allRecommendations.length === 0) {
            throw new Error(t('messages.noRecommendationsGenerated'));
          }

          // OPTIMISATION: Sauvegarder en base par chunks pour éviter les timeouts
          if (process.env.NODE_ENV === 'development') {
          console.log("🗑️ Suppression des anciennes recommandations...");
          }
          await db.delete(recommendations)
            .where(eq(recommendations.userId, userId));

          if (process.env.NODE_ENV === 'development') {
          console.log(`💾 Sauvegarde de ${allRecommendations.length} recommandations par chunks...`);
          }
          let insertedRecommendations: any[] = [];

          const saveChunkSize = 50; // Sauvegarder par chunks de 50
          for (let i = 0; i < allRecommendations.length; i += saveChunkSize) {
            const chunk = allRecommendations.slice(i, i + saveChunkSize);
            try {
              const chunkResults = await db.insert(recommendations)
                .values(chunk)
                .onConflictDoUpdate({
                  target: [recommendations.userId, recommendations.spotifyAlbumId, recommendations.timeframe],
                  set: {
                    artistName: sql`excluded."artistName"`,
                    albumTitle: sql`excluded."albumTitle"`,
                    albumCoverUrl: sql`excluded."albumCoverUrl"`,
                    listenScore: sql`excluded."listenScore"`,
                    generatedAt: sql`excluded."generatedAt"`,
                    isOwned: sql`excluded."isOwned"`,
                    topTrackName: sql`excluded."topTrackName"`,
                    topTrackId: sql`excluded."topTrackId"`,
                    topTrackPreviewUrl: sql`excluded."topTrackPreviewUrl"`,
                    topTrackListenScore: sql`excluded."topTrackListenScore"`
                  }
                })
                .returning();
              insertedRecommendations.push(...chunkResults);
            } catch (error) {
              console.error(`❌ Erreur lors de la sauvegarde du chunk ${i}-${i + saveChunkSize}:`, error);
              // Essayer d'insérer les recommandations une par une pour identifier le problème
              for (const rec of chunk) {
                try {
                  const result = await db.insert(recommendations)
                    .values(rec)
                    .onConflictDoUpdate({
                      target: [recommendations.userId, recommendations.spotifyAlbumId, recommendations.timeframe],
                      set: {
                        artistName: sql`excluded."artistName"`,
                        albumTitle: sql`excluded."albumTitle"`,
                        albumCoverUrl: sql`excluded."albumCoverUrl"`,
                        listenScore: sql`excluded."listenScore"`,
                        generatedAt: sql`excluded."generatedAt"`,
                        isOwned: sql`excluded."isOwned"`,
                        topTrackName: sql`excluded."topTrackName"`,
                        topTrackId: sql`excluded."topTrackId"`,
                        topTrackPreviewUrl: sql`excluded."topTrackPreviewUrl"`,
                        topTrackListenScore: sql`excluded."topTrackListenScore"`
                      }
                    })
                    .returning();
                  insertedRecommendations.push(...result);
                } catch (individualError) {
                  console.error(`❌ Erreur pour la recommandation ${rec.artistName} - ${rec.albumTitle}:`, individualError);
                }
              }
            }
          }

          // ÉTAPE 3: Recherche des offres
          sendEvent({
            type: 'step_update',
            step: 'offers',
            status: 'in_progress',
            message: t('messages.searchingOffers')
          });

          // Pas de status_update ici - on laisse place aux messages de découverte

          // Continuer à afficher des découvertes pendant la recherche d'offres
          const continueDiscoveryDuringOffers = () => {
            if (discoveredAlbums.length > 0) {
              const randomAlbum = discoveredAlbums[Math.floor(Math.random() * discoveredAlbums.length)];
              if (process.env.NODE_ENV === 'development') {
              console.log(`🎵 Envoi découverte offres: ${randomAlbum.artist} - ${randomAlbum.album}`);
              }
              sendEvent({
                type: 'status_update',
                message: t('messages.likeAlbum', { album: randomAlbum.album || 'Album', artist: randomAlbum.artist })
              });
            } else {
              if (process.env.NODE_ENV === 'development') {
              console.log(`⚠️ Aucun album découvert disponible pour les offres`);
              }
            }
          };

          // Envoyer des découvertes pendant la recherche d'offres
          const offersDiscoveryInterval = setInterval(continueDiscoveryDuringOffers, 1300);

          // Démarrer immédiatement la première découverte
          continueDiscoveryDuringOffers();

          // OPTIMISATION: Enrichir avec Amazon Phase 1 (liens de recherche) - traitement rapide
          let enrichedRecommendations = enrichWithAmazonSearchLinks(insertedRecommendations);

          // OPTIMISATION: Sauvegarder les liens Amazon par chunks en parallèle
          if (process.env.NODE_ENV === 'development') {
          console.log("💾 Sauvegarde des liens Amazon en base de données...");
          }
          const updatePromises = enrichedRecommendations
            .filter(rec => rec.affiliateLinks && rec.affiliateLinks.length > 0)
            .map(rec =>
              db.update(recommendations)
                .set({
                  affiliateLinks: JSON.stringify(rec.affiliateLinks)
                })
                .where(eq(recommendations.id, rec.id))
                .catch(error => {
                  console.error(`❌ Erreur sauvegarde liens Amazon pour ${rec.artistName}:`, error);
                })
            );

          // Attendre toutes les mises à jour en parallèle
          await Promise.all(updatePromises);

          // Synchroniser la wishlist avec les nouvelles données des recommandations
          try {
            const { syncWishlistWithRecommendations } = await import('@/app/actions/wishlist');
            await syncWishlistWithRecommendations(userId, enrichedRecommendations);
          } catch (syncError) {
            if (process.env.NODE_ENV === 'development') {
            console.warn('⚠️ Erreur lors de la synchronisation de la wishlist:', syncError);
            }
            // Ne pas faire échouer la génération si la synchronisation échoue
          }

          // TODO: Rakuten temporairement désactivé à cause d'erreurs API
          if (process.env.NODE_ENV === 'development') {
          console.log("🔗 Rakuten temporairement désactivé - utilisation d'Amazon Phase 1 uniquement");
          }

          sendEvent({
            type: 'counter_update',
            entity: 'offers_found',
            value: enrichedRecommendations.filter(r => r.affiliateLinks && r.affiliateLinks.length > 0).length
          });

          // Arrêter les découvertes de la phase offres
          clearInterval(offersDiscoveryInterval);

          sendEvent({
            type: 'step_update',
            step: 'offers',
            status: 'completed',
            message: t('messages.amazonLinksGenerated')
          });

          // ÉTAPE 4: Finalisation et envoi progressif des résultats
          sendEvent({
            type: 'step_update',
            step: 'finalizing',
            status: 'in_progress',
            message: t('messages.finalizingRecommendations')
          });

          // Pas de status_update ici - on laisse place aux messages de découverte

          // Continuer à afficher des découvertes pendant la finalisation
          const continueDiscoveryDuringFinalization = () => {
            if (discoveredAlbums.length > 0) {
              const randomAlbum = discoveredAlbums[Math.floor(Math.random() * discoveredAlbums.length)];
              if (process.env.NODE_ENV === 'development') {
              console.log(`🎵 Envoi découverte finalisation: ${randomAlbum.artist} - ${randomAlbum.album}`);
              }
              sendEvent({
                type: 'status_update',
                message: t('messages.likeAlbum', { album: randomAlbum.album || 'Album', artist: randomAlbum.artist })
              });
            } else {
              if (process.env.NODE_ENV === 'development') {
              console.log(`⚠️ Aucun album découvert disponible pour la finalisation`);
              }
            }
          };

          // Envoyer des découvertes pendant la finalisation
          const finalizationDiscoveryInterval = setInterval(continueDiscoveryDuringFinalization, 1300);

          // Démarrer immédiatement la première découverte
          continueDiscoveryDuringFinalization();

          // Trier et limiter à 50 recommandations
          const finalRecommendations = enrichedRecommendations
            .sort((a, b) => b.listenScore - a.listenScore)
            .slice(0, 50);

          // OPTIMISATION: Envoyer les recommandations par chunks pour plus de rapidité
          const chunkSize = 10;
          for (let i = 0; i < finalRecommendations.length; i += chunkSize) {
            const chunk = finalRecommendations.slice(i, i + chunkSize);

            // Envoyer toutes les recommandations du chunk en parallèle
            for (const recommendation of chunk) {
              sendEvent({
                type: 'recommendation_ready',
                payload: {
                  ...recommendation,
                  isWishlisted: false // À déterminer côté client si nécessaire
                }
              });
            }

            // Mettre à jour le compteur pour le chunk
            sendEvent({
              type: 'counter_update',
              entity: 'recommendations_generated',
              value: Math.min(i + chunkSize, finalRecommendations.length)
            });

            // Délai minimal entre les chunks pour maintenir la fluidité
            if (i + chunkSize < finalRecommendations.length) {
              await new Promise(resolve => setTimeout(resolve, 100));
            }
          }

          sendEvent({
            type: 'step_update',
            step: 'finalizing',
            status: 'completed',
            message: t('messages.recommendationsReady', { count: finalRecommendations.length })
          });

          // Insight final
          sendEvent({
            type: 'insight',
            message: t('messages.foundPerfectAlbums', { count: finalRecommendations.length })
          });

          // OPTIMISATION: Continuer les découvertes pendant le délai pour éviter le trou d'affichage
          let delayElapsed = 0;
          const delayInterval = 300; // Intervalle de 300ms pour les découvertes
          const totalDelay = 1500; // Délai total de 1,5s

          while (delayElapsed < totalDelay) {
            // Continuer à afficher des découvertes pour combler le trou
            if (discoveredAlbums.length > 0) {
              const randomAlbum = discoveredAlbums[Math.floor(Math.random() * discoveredAlbums.length)];
              sendEvent({
                type: 'status_update',
                message: t('messages.likeAlbum', { album: randomAlbum.album || 'Album', artist: randomAlbum.artist })
              });
            }

            await new Promise(resolve => setTimeout(resolve, delayInterval));
            delayElapsed += delayInterval;
          }

          // Arrêter les découvertes de finalisation maintenant
          clearInterval(finalizationDiscoveryInterval);

          // Message final avant redirection
          sendEvent({
            type: 'status_update',
            message: t('messages.yourRecommendationsAreReady')
          });

          // Événement de fin
          sendEvent({
            type: 'complete'
          });

          if (process.env.NODE_ENV === 'development') {
          console.log(`✅ Génération interactive terminée pour l'utilisateur ${userId}`);
          }

          // NOUVEAU: Déclencher l'email de première recommandation après génération réussie
          setTimeout(async () => {
            try {
              if (process.env.NODE_ENV === 'development') {
              console.log(`🎯 Déclenchement de la vérification d'email de première recommandation pour ${userId}`);
              }
              await triggerFirstRecommendationEmailIfNeeded(userId);
              if (process.env.NODE_ENV === 'development') {
              console.log(`✅ Vérification d'email de première recommandation terminée pour ${userId}`);
              }
            } catch (error) {
              console.error(`❌ Erreur lors de l'envoi de l'email de première recommandation pour ${userId}:`, error);
            }
          }, 1000); // Délai pour laisser le temps au client de traiter la completion

        } catch (error) {
          console.error(`❌ Erreur lors de la génération interactive:`, error);
          sendEvent({
            type: 'error',
            message: error instanceof Error ? error.message : t('messages.genericError')
          });
        } finally {
          // Nettoyer l'intervalle de vérification de connexion
          clearInterval(connectionCheckInterval);

          // Marquer comme fermé avant de fermer pour éviter les erreurs
          isControllerClosed = true;

          // Petit délai pour s'assurer que le dernier événement est envoyé
          setTimeout(() => {
            try {
              controller.close();
              if (process.env.NODE_ENV === 'development') {
              console.log("🔒 Controller SSE fermé proprement");
              }
            } catch (error) {
              // Ignorer les erreurs de fermeture si le controller est déjà fermé
              if (!(error instanceof Error && error.message?.includes('Controller is already closed'))) {
                console.error("❌ Erreur lors de la fermeture du controller SSE:", error);
              }
            }
          }, 100);
        }
      };

      // Démarrer la génération
      generateRecommendations();
    }
  });

  return new Response(stream, { headers });
}
